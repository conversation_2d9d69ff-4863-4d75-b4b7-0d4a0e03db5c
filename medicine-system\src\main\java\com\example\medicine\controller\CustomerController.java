package com.example.medicine.controller;

import com.example.medicine.common.Result;
import com.example.medicine.common.PageResult;
import com.example.medicine.entity.Customer;
import com.example.medicine.service.CustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/customer")
public class CustomerController {
    @Autowired
    private CustomerService customerService;

    @PostMapping("/add")
    public Result<Customer> addCustomer(@RequestBody Customer customer) {
        try {
            return Result.success(customerService.addCustomer(customer));
        } catch (Exception e) {
            return Result.error("添加失败: " + e.getMessage());
        }
    }

    @PutMapping("/update/{id}")
    public Result<Customer> updateCustomer(@PathVariable Long id, @RequestBody Customer customer) {
        try {
            return Result.success(customerService.updateCustomer(id, customer));
        } catch (Exception e) {
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/delete/{id}")
    public Result<Void> deleteCustomer(@PathVariable Long id) {
        try {
            customerService.deleteCustomer(id);
            return Result.success(null);
        } catch (Exception e) {
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public Result<Customer> getCustomerById(@PathVariable Long id) {
        try {
            return Result.success(customerService.getCustomerById(id));
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/list")
    public Result<PageResult<Customer>> getCustomerList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "") String keyword) {
        try {
            List<Customer> allCustomers = customerService.getAllCustomers();

            // 如果有关键字搜索，进行过滤
            if (!keyword.isEmpty()) {
                allCustomers = allCustomers.stream()
                    .filter(customer ->
                        customer.getName().toLowerCase().contains(keyword.toLowerCase()) ||
                        customer.getContact().toLowerCase().contains(keyword.toLowerCase()) ||
                        (customer.getAddress() != null && customer.getAddress().toLowerCase().contains(keyword.toLowerCase()))
                    )
                    .collect(java.util.stream.Collectors.toList());
            }

            // 简单的分页逻辑
            int total = allCustomers.size();
            int start = (page - 1) * size;
            int end = Math.min(start + size, total);

            List<Customer> records = start < total ? allCustomers.subList(start, end) : List.of();

            PageResult<Customer> pageResult = new PageResult<>();
            pageResult.setRecords(records);
            pageResult.setTotal(total);
            pageResult.setCurrent(page);
            pageResult.setSize(size);

            return Result.success(pageResult);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/all")
    public Result<List<Customer>> getAllCustomers() {
        try {
            return Result.success(customerService.getAllCustomers());
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }
}